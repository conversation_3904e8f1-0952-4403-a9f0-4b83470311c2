let salaries = {};
let taxes = {};

async function loadData() {
  try {
    const salaryRes = await fetch('salaries.json');
    salaries = await salaryRes.json();

    const taxRes = await fetch('taxes.json');
    taxes = await taxRes.json();

    populateGrades();
  } catch (error) {
    console.error("فشل تحميل البيانات:", error);
  }
}

function populateGrades() {
  const gradeSelect = document.getElementById('grade');
  gradeSelect.innerHTML = '';

  for (let grade in salaries) {
    const option = document.createElement('option');
    option.value = grade;
    option.textContent = grade;
    gradeSelect.appendChild(option);
  }

  gradeSelect.addEventListener('change', populateSteps);
  populateSteps();
}

function populateSteps() {
  const grade = document.getElementById('grade').value;
  const stepSelect = document.getElementById('step');
  stepSelect.innerHTML = '';

  if (!salaries[grade]) return;

  for (let step in salaries[grade]) {
    const option = document.createElement('option');
    option.value = step;
    option.textContent = step;
    stepSelect.appendChild(option);
  }
}

function calculate() {
  const grade = document.getElementById('grade').value;
  const step = document.getElementById('step').value;
  const children = document.getElementById('children').value;

  const baseSalary = salaries[grade]?.[step] || 0;
  let taxRate = 0;

  const brackets = taxes[children] || [];

  for (let bracket of brackets) {
    if (baseSalary >= bracket.min && baseSalary <= bracket.max) {
      taxRate = bracket.rate;
      break;
    }
  }

  const taxAmount = Math.round((baseSalary * taxRate) / 100);

  document.getElementById('salary').textContent = `الراتب الاسمي: ${baseSalary} دينار`;
  document.getElementById('tax').textContent = `الضريبة: ${taxAmount} دينار (${taxRate}%)`;
}

loadData();
