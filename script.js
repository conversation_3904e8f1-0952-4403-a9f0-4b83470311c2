// بيانات الرواتب الكاملة
const salariesData = {
    "الدرجة 1": {
        "استيب 1": 850000,
        "استيب 2": 870000,
        "استيب 3": 890000,
        "استيب 4": 910000,
        "استيب 5": 930000,
        "استيب 6": 950000,
        "استيب 7": 970000,
        "استيب 8": 990000,
        "استيب 9": 1010000,
        "استيب 10": 1030000
    },
    "الدرجة 2": {
        "استيب 1": 750000,
        "استيب 2": 770000,
        "استيب 3": 790000,
        "استيب 4": 810000,
        "استيب 5": 830000,
        "استيب 6": 850000,
        "استيب 7": 870000,
        "استيب 8": 890000,
        "استيب 9": 910000,
        "استيب 10": 930000
    },
    "الدرجة 3": {
        "استيب 1": 650000,
        "استيب 2": 670000,
        "استيب 3": 690000,
        "استيب 4": 710000,
        "استيب 5": 730000,
        "استيب 6": 750000,
        "استيب 7": 770000,
        "استيب 8": 790000,
        "استيب 9": 810000,
        "استيب 10": 830000
    },
    "الدرجة 4": {
        "استيب 1": 550000,
        "استيب 2": 570000,
        "استيب 3": 590000,
        "استيب 4": 610000,
        "استيب 5": 630000,
        "استيب 6": 650000,
        "استيب 7": 670000,
        "استيب 8": 690000,
        "استيب 9": 710000,
        "استيب 10": 730000
    },
    "الدرجة 5": {
        "استيب 1": 450000,
        "استيب 2": 470000,
        "استيب 3": 490000,
        "استيب 4": 510000,
        "استيب 5": 530000,
        "استيب 6": 550000,
        "استيب 7": 570000,
        "استيب 8": 590000,
        "استيب 9": 610000,
        "استيب 10": 630000
    },
    "الدرجة 6": {
        "استيب 1": 380000,
        "استيب 2": 400000,
        "استيب 3": 420000,
        "استيب 4": 440000,
        "استيب 5": 460000,
        "استيب 6": 480000,
        "استيب 7": 500000,
        "استيب 8": 520000,
        "استيب 9": 540000,
        "استيب 10": 560000
    },
    "الدرجة 7": {
        "استيب 1": 320000,
        "استيب 2": 340000,
        "استيب 3": 360000,
        "استيب 4": 380000,
        "استيب 5": 400000,
        "استيب 6": 420000,
        "استيب 7": 440000,
        "استيب 8": 460000,
        "استيب 9": 480000,
        "استيب 10": 500000
    },
    "الدرجة 8": {
        "استيب 1": 280000,
        "استيب 2": 300000,
        "استيب 3": 320000,
        "استيب 4": 340000,
        "استيب 5": 360000,
        "استيب 6": 380000,
        "استيب 7": 400000,
        "استيب 8": 420000,
        "استيب 9": 440000,
        "استيب 10": 460000
    },
    "الدرجة 9": {
        "استيب 1": 250000,
        "استيب 2": 270000,
        "استيب 3": 290000,
        "استيب 4": 310000,
        "استيب 5": 330000,
        "استيب 6": 350000,
        "استيب 7": 370000,
        "استيب 8": 390000,
        "استيب 9": 410000,
        "استيب 10": 430000
    },
    "الدرجة 10": {
        "استيب 1": 220000,
        "استيب 2": 240000,
        "استيب 3": 260000,
        "استيب 4": 280000,
        "استيب 5": 300000,
        "استيب 6": 320000,
        "استيب 7": 340000,
        "استيب 8": 360000,
        "استيب 9": 380000,
        "استيب 10": 400000
    }
};

// بيانات الضرائب الكاملة
const taxesData = {
    "a": [ // أعزب
        { min: 0, max: 250000, rate: 0 },
        { min: 250001, max: 500000, rate: 3 },
        { min: 500001, max: 750000, rate: 5 },
        { min: 750001, max: 1000000, rate: 10 },
        { min: 1000001, max: 1500000, rate: 15 },
        { min: 1500001, max: Infinity, rate: 20 }
    ],
    "0": [ // متزوج بدون أطفال
        { min: 0, max: 300000, rate: 0 },
        { min: 300001, max: 600000, rate: 3 },
        { min: 600001, max: 900000, rate: 5 },
        { min: 900001, max: 1200000, rate: 10 },
        { min: 1200001, max: 1800000, rate: 15 },
        { min: 1800001, max: Infinity, rate: 20 }
    ],
    "1": [ // طفل واحد
        { min: 0, max: 350000, rate: 0 },
        { min: 350001, max: 700000, rate: 2 },
        { min: 700001, max: 1050000, rate: 4 },
        { min: 1050001, max: 1400000, rate: 8 },
        { min: 1400001, max: 2100000, rate: 12 },
        { min: 2100001, max: Infinity, rate: 18 }
    ],
    "2": [ // طفلان
        { min: 0, max: 400000, rate: 0 },
        { min: 400001, max: 800000, rate: 2 },
        { min: 800001, max: 1200000, rate: 4 },
        { min: 1200001, max: 1600000, rate: 7 },
        { min: 1600001, max: 2400000, rate: 10 },
        { min: 2400001, max: Infinity, rate: 15 }
    ],
    "3": [ // 3 أطفال
        { min: 0, max: 450000, rate: 0 },
        { min: 450001, max: 900000, rate: 1 },
        { min: 900001, max: 1350000, rate: 3 },
        { min: 1350001, max: 1800000, rate: 6 },
        { min: 1800001, max: 2700000, rate: 9 },
        { min: 2700001, max: Infinity, rate: 12 }
    ],
    "4": [ // 4 أطفال
        { min: 0, max: 500000, rate: 0 },
        { min: 500001, max: 1000000, rate: 1 },
        { min: 1000001, max: 1500000, rate: 3 },
        { min: 1500001, max: 2000000, rate: 5 },
        { min: 2000001, max: 3000000, rate: 8 },
        { min: 3000001, max: Infinity, rate: 10 }
    ],
    "5": [ // 5 أطفال
        { min: 0, max: 550000, rate: 0 },
        { min: 550001, max: 1100000, rate: 1 },
        { min: 1100001, max: 1650000, rate: 2 },
        { min: 1650001, max: 2200000, rate: 4 },
        { min: 2200001, max: 3300000, rate: 7 },
        { min: 3300001, max: Infinity, rate: 9 }
    ],
    "6": [ // 6 أطفال
        { min: 0, max: 600000, rate: 0 },
        { min: 600001, max: 1200000, rate: 0.5 },
        { min: 1200001, max: 1800000, rate: 2 },
        { min: 1800001, max: 2400000, rate: 3 },
        { min: 2400001, max: 3600000, rate: 6 },
        { min: 3600001, max: Infinity, rate: 8 }
    ],
    "7": [ // 7 أطفال
        { min: 0, max: 650000, rate: 0 },
        { min: 650001, max: 1300000, rate: 0.5 },
        { min: 1300001, max: 1950000, rate: 1.5 },
        { min: 1950001, max: 2600000, rate: 3 },
        { min: 2600001, max: 3900000, rate: 5 },
        { min: 3900001, max: Infinity, rate: 7 }
    ],
    "8": [ // 8 أطفال
        { min: 0, max: 700000, rate: 0 },
        { min: 700001, max: 1400000, rate: 0 },
        { min: 1400001, max: 2100000, rate: 1 },
        { min: 2100001, max: 2800000, rate: 2 },
        { min: 2800001, max: 4200000, rate: 4 },
        { min: 4200001, max: Infinity, rate: 6 }
    ],
    "9": [ // 9 أطفال
        { min: 0, max: 750000, rate: 0 },
        { min: 750001, max: 1500000, rate: 0 },
        { min: 1500001, max: 2250000, rate: 1 },
        { min: 2250001, max: 3000000, rate: 2 },
        { min: 3000001, max: 4500000, rate: 3 },
        { min: 4500001, max: Infinity, rate: 5 }
    ],
    "10": [ // 10 أطفال
        { min: 0, max: 800000, rate: 0 },
        { min: 800001, max: 1600000, rate: 0 },
        { min: 1600001, max: 2400000, rate: 0.5 },
        { min: 2400001, max: 3200000, rate: 1 },
        { min: 3200001, max: 4800000, rate: 2 },
        { min: 4800001, max: Infinity, rate: 4 }
    ]
};

// متغيرات عامة
let currentGrade = '';
let currentStep = '';
let currentChildren = '';

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeForm();
    addEventListeners();
});

// تهيئة النموذج
function initializeForm() {
    populateGrades();
    hideResults();
}

// إضافة مستمعي الأحداث
function addEventListeners() {
    const gradeSelect = document.getElementById('grade');
    const stepSelect = document.getElementById('step');
    const childrenSelect = document.getElementById('children');

    gradeSelect.addEventListener('change', function() {
        currentGrade = this.value;
        populateSteps();
        validateForm();
    });

    stepSelect.addEventListener('change', function() {
        currentStep = this.value;
        validateForm();
    });

    childrenSelect.addEventListener('change', function() {
        currentChildren = this.value;
        validateForm();
    });
}

// ملء قائمة الدرجات
function populateGrades() {
    const gradeSelect = document.getElementById('grade');
    gradeSelect.innerHTML = '<option value="">-- اختر الدرجة --</option>';

    for (let grade in salariesData) {
        const option = document.createElement('option');
        option.value = grade;
        option.textContent = grade;
        gradeSelect.appendChild(option);
    }
}

// ملء قائمة الاستيبات
function populateSteps() {
    const stepSelect = document.getElementById('step');
    stepSelect.innerHTML = '<option value="">-- اختر الاستيب --</option>';

    if (!currentGrade || !salariesData[currentGrade]) {
        stepSelect.disabled = true;
        return;
    }

    stepSelect.disabled = false;

    for (let step in salariesData[currentGrade]) {
        const option = document.createElement('option');
        option.value = step;
        option.textContent = step;
        stepSelect.appendChild(option);
    }
}

// التحقق من صحة النموذج
function validateForm() {
    const calculateBtn = document.getElementById('calculateBtn');
    const isValid = currentGrade && currentStep && currentChildren !== '';

    calculateBtn.disabled = !isValid;
    calculateBtn.style.opacity = isValid ? '1' : '0.6';
    calculateBtn.style.cursor = isValid ? 'pointer' : 'not-allowed';
}

// إخفاء النتائج
function hideResults() {
    const resultsSection = document.getElementById('results');
    resultsSection.style.display = 'none';
}

// إظهار النتائج
function showResults() {
    const resultsSection = document.getElementById('results');
    resultsSection.style.display = 'block';
    resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
}

// تنسيق الأرقام
function formatNumber(number) {
    return new Intl.NumberFormat('ar-IQ').format(number);
}

// حساب الضريبة
function calculateTax(salary, childrenCount) {
    const brackets = taxesData[childrenCount];
    if (!brackets) return { rate: 0, amount: 0 };

    for (let bracket of brackets) {
        if (salary >= bracket.min && salary <= bracket.max) {
            const taxAmount = Math.round((salary * bracket.rate) / 100);
            return { rate: bracket.rate, amount: taxAmount };
        }
    }

    return { rate: 0, amount: 0 };
}

// الوظيفة الرئيسية للحساب
function calculate() {
    // التحقق من البيانات
    if (!currentGrade || !currentStep || currentChildren === '') {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // الحصول على الراتب الأساسي
    const baseSalary = salariesData[currentGrade][currentStep];
    if (!baseSalary) {
        alert('خطأ في البيانات المدخلة');
        return;
    }

    // حساب الضريبة
    const taxInfo = calculateTax(baseSalary, currentChildren);
    const netSalary = baseSalary - taxInfo.amount;

    // عرض النتائج
    displayResults(baseSalary, taxInfo.amount, taxInfo.rate, netSalary);
    showResults();
}

// عرض النتائج
function displayResults(salary, tax, taxRate, netSalary) {
    document.getElementById('salaryAmount').textContent = formatNumber(salary);
    document.getElementById('taxAmount').textContent = formatNumber(tax);
    document.getElementById('netAmount').textContent = formatNumber(netSalary);
    document.getElementById('taxRate').textContent = `${taxRate}%`;

    // إضافة تأثيرات بصرية
    animateNumbers();
}

// تحريك الأرقام
function animateNumbers() {
    const amounts = document.querySelectorAll('.card-amount');
    amounts.forEach(amount => {
        amount.style.transform = 'scale(1.1)';
        setTimeout(() => {
            amount.style.transform = 'scale(1)';
        }, 200);
    });
}
