/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Arial', sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #ffffff 50%, #ADD8E6 100%);
    min-height: 100vh;
    direction: rtl;
    text-align: right;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* العنوان المتحرك */
.animated-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px 0;
}

.main-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.word {
    display: inline-block;
    margin: 0 10px;
    animation: wave 3s ease-in-out infinite;
    transition: all 0.3s ease;
}

.word:hover {
    transform: scale(1.1);
    text-shadow: 3px 3px 6px rgba(0,0,0,0.2);
}

.word.blue {
    color: #0077b6;
    animation-delay: 0s;
}

.word.orange {
    color: #ff8500;
    animation-delay: 0.5s;
}

.word:nth-child(3) {
    animation-delay: 1s;
}

.word:nth-child(4) {
    animation-delay: 1.5s;
}

.word:nth-child(5) {
    animation-delay: 2s;
}

@keyframes wave {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.subtitle {
    font-size: 1.5rem;
    color: #555;
    font-weight: 400;
    opacity: 0;
    animation: fadeInUp 1s ease-out 1s forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* النموذج الرئيسي */
.main-form {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    margin-bottom: 30px;
}

.form-section {
    margin-bottom: 40px;
}

.section-title {
    font-size: 2rem;
    color: #0077b6;
    text-align: center;
    margin-bottom: 30px;
    font-weight: 600;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #0077b6, #ff8500);
    border-radius: 2px;
}

/* صفوف النموذج */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.icon {
    font-size: 1.3rem;
}

.form-select {
    width: 100%;
    padding: 15px 20px;
    font-size: 1.1rem;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    background: white;
    color: #333;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.form-select:focus {
    outline: none;
    border-color: #0077b6;
    box-shadow: 0 0 0 3px rgba(0, 119, 182, 0.1);
    transform: translateY(-2px);
}

.form-select:disabled {
    background-color: #f5f5f5;
    color: #999;
    cursor: not-allowed;
}

/* زر الحساب */
.calculate-btn {
    width: 100%;
    padding: 18px 30px;
    font-size: 1.3rem;
    font-weight: 600;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
    font-family: 'Cairo', sans-serif;
    box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
}

.calculate-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(40, 167, 69, 0.4);
    background: linear-gradient(135deg, #218838, #1ea080);
}

.calculate-btn:active {
    transform: translateY(-1px);
}

.btn-icon {
    font-size: 1.4rem;
}

/* قسم النتائج */
.results-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.result-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.result-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 15px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0,0,0,0.08);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.result-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #0077b6, #ff8500);
}

.result-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.12);
    border-color: rgba(0, 119, 182, 0.3);
}

.salary-card::before {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.tax-card::before {
    background: linear-gradient(90deg, #dc3545, #fd7e14);
}

.net-card::before {
    background: linear-gradient(90deg, #0077b6, #6f42c1);
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.8;
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #555;
    margin-bottom: 15px;
}

.card-amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: #0077b6;
    margin-bottom: 10px;
    direction: ltr;
    text-align: center;
}

.card-currency {
    font-size: 1rem;
    color: #777;
    font-weight: 500;
}

/* تفاصيل الضريبة */
.tax-details {
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(0, 119, 182, 0.2);
}

.tax-rate {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
}

.rate-value {
    color: #0077b6;
    font-weight: 700;
    font-size: 1.4rem;
}

/* التذييل */
.footer {
    text-align: center;
    padding: 30px 20px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    margin-top: 40px;
    color: #666;
    font-size: 1rem;
    line-height: 1.6;
}

.footer p {
    margin-bottom: 5px;
}

/* تأثيرات الحركة */
.form-group {
    animation: fadeInLeft 0.6s ease-out;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تنسيقات الهاتف المحمول */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .main-title {
        font-size: 2.5rem;
    }

    .word {
        margin: 0 5px;
        display: inline-block;
    }

    .main-form {
        padding: 25px;
    }

    .result-cards {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .card-amount {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .main-title {
        font-size: 2rem;
    }

    .word {
        display: block;
        margin: 5px 0;
    }

    .form-select {
        padding: 12px 15px;
        font-size: 1rem;
    }

    .calculate-btn {
        padding: 15px 25px;
        font-size: 1.1rem;
    }
}

/* تأثيرات إضافية */
.form-select option {
    padding: 10px;
    font-size: 1rem;
}

/* تحسين الألوان للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .main-form, .results-section {
        background: rgba(30, 30, 30, 0.95);
        color: #fff;
    }

    .form-select {
        background: #333;
        color: #fff;
        border-color: #555;
    }

    .card-title {
        color: #ccc;
    }
}
